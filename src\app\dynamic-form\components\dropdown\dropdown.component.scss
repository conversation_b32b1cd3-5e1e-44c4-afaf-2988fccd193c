@use '../../../../styles/shared.scss' as *;

/* Dropdown Input Container - EXACT from regular-field component lines 95-151 */
.dropdown-input-container {
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
  min-width: 200px;
}

.dropdown-input-container .form-input {
  flex: 1;
  padding-right: 45px; /* Space for arrow button */
  border-radius: 8px 0 0 8px;
  border-right: none;
  font-family: 'Poppins', sans-serif;
  font-size: 14px;
  color: #495057;
  background-color: white;
  border: 1px solid #DBDBDB;
  padding: 12px 16px;
  transition: all 0.3s ease;
  outline: none;
  box-sizing: border-box;
}

.dropdown-input-container .form-input:focus {
  border-color: #283A97;
  box-shadow: 0 0 0 2px rgba(40, 58, 151, 0.1);
}

.dropdown-input-container .dropdown-arrow-btn {
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  width: 40px;
  height: 100%;
  border: 1px solid #DBDBDB;
  border-left: none;
  border-radius: 0 8px 8px 0;
  background-color: #f8f9fa;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  z-index: 1;
}

.dropdown-input-container .dropdown-arrow-btn:hover {
  background-color: #e9ecef;
  border-color: #283A97;
}

.dropdown-input-container .dropdown-arrow-btn:focus {
  outline: none;
  border-color: #283A97;
  box-shadow: 0 0 0 2px rgba(40, 58, 151, 0.1);
}

.dropdown-input-container .dropdown-arrow-btn .mat-icon {
  font-size: 20px;
  width: 20px;
  height: 20px;
  color: #6c757d;
  transition: color 0.3s ease;
}

.dropdown-input-container .dropdown-arrow-btn:hover .mat-icon {
  color: #283A97;
}

/* Dropdown List Styles - EXACT from regular-field component lines 154-193 */
.dropdown-list {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  z-index: 1000;
  background-color: white;
  border: 1px solid #DBDBDB;
  border-top: none;
  border-radius: 0 0 8px 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  max-height: 200px;
  overflow-y: auto;
}

.dropdown-item {
  padding: 12px 16px;
  cursor: pointer;
  border-bottom: 1px solid #f1f3f4;
  font-family: 'Poppins', sans-serif;
  font-size: 14px;
  color: #495057;
  transition: background-color 0.2s ease;
}

.dropdown-item:hover {
  background-color: #f8f9fa;
  color: #283A97;
}

.dropdown-item:last-child {
  border-bottom: none;
}

.dropdown-empty {
  padding: 12px 16px;
  color: #6c757d;
  font-style: italic;
  text-align: center;
}

.dropdown-loading {
  padding: 12px 16px;
  color: #6c757d;
  font-style: italic;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.dropdown-loading .mat-icon {
  animation: spin 1s linear infinite;
  font-size: 16px;
  width: 16px;
  height: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Disabled State Styling - EXACT from regular-field component lines 271-282 */
.dropdown-input-container.disabled .form-input,
.dropdown-input-container .form-input.disabled,
.dropdown-input-container .form-input[disabled],
.dropdown-input-container .form-input[readonly] {
  background-color: #f1f3f4 !important;
  color: #5f6368 !important;
  cursor: not-allowed !important;
  border-color: #dadce0 !important;
  opacity: 1 !important;
  pointer-events: none !important;
}

.dropdown-input-container.disabled .dropdown-arrow-btn,
.dropdown-input-container .dropdown-arrow-btn[disabled] {
  background-color: #f1f3f4 !important;
  border-color: #dadce0 !important;
  cursor: not-allowed !important;
  pointer-events: none !important;
}

.dropdown-input-container.disabled .dropdown-arrow-btn .mat-icon,
.dropdown-input-container .dropdown-arrow-btn[disabled] .mat-icon {
  color: #5f6368 !important;
}

/* Responsive dropdown styling - EXACT from regular-field component lines 427-491 */
@media (max-width: 768px) {
  .dropdown-input-container .dropdown-arrow-btn {
    width: 36px;
    height: 36px;
  }

  .dropdown-input-container .dropdown-arrow-btn .mat-icon {
    font-size: 18px;
    width: 18px;
    height: 18px;
  }

  .dropdown-item {
    padding: 10px 12px;
    font-size: 13px;
  }

  .dropdown-empty {
    padding: 10px 12px;
    font-size: 13px;
  }

  .dropdown-loading {
    padding: 10px 12px;
    font-size: 13px;
  }
}

@media (max-width: 480px) {
  .dropdown-input-container .dropdown-arrow-btn {
    width: 32px;
    height: 32px;
  }

  .dropdown-input-container .dropdown-arrow-btn .mat-icon {
    font-size: 16px;
    width: 16px;
    height: 16px;
  }

  .dropdown-item {
    padding: 8px 10px;
    font-size: 12px;
  }

  .dropdown-empty {
    padding: 8px 10px;
    font-size: 12px;
  }

  .dropdown-loading {
    padding: 8px 10px;
    font-size: 12px;
  }
}

/* Scrollbar styling for dropdown lists - EXACT from regular-field component lines 494-510 */
.dropdown-list::-webkit-scrollbar {
  width: 6px;
}

.dropdown-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.dropdown-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.dropdown-list::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Ensure dropdown containers work well in multi-field and grouped contexts - EXACT from regular-field component lines 523-532 */
.multi-input .dropdown-input-container,
.group-fields .dropdown-input-container {
  width: 100%;
  min-width: 200px;
}

/* Adjust dropdown positioning in grouped fields */
.grouped-field-section .dropdown-list {
  z-index: 1001;
}
