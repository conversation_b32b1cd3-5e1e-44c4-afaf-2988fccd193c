import { ComponentFixture, TestBed, fakeAsync, tick } from '@angular/core/testing';
import { FormControl } from '@angular/forms';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { DebugElement } from '@angular/core';
import { By } from '@angular/platform-browser';

import { DropdownComponent, DropdownConfig, DropdownOption } from './dropdown.component';
import { environment } from '../../../../../environments/environment';

describe('DropdownComponent', () => {
  let component: DropdownComponent;
  let fixture: ComponentFixture<DropdownComponent>;
  let httpMock: HttpTestingController;

  const mockOptions: DropdownOption[] = [
    { ROW_ID: 'option1', name: 'Option 1' },
    { ROW_ID: 'option2', name: 'Option 2' },
    { ROW_ID: 'option3', name: 'Option 3' }
  ];

  const defaultConfig: DropdownConfig = {
    type: 'regular',
    queryBuilderId: 'testQuery',
    searchEnabled: true,
    placeholder: 'Select option',
    emptyMessage: 'No options found',
    tooltip: 'Show options'
  };

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        DropdownComponent,
        HttpClientTestingModule,
        MatIconModule,
        MatTooltipModule,
        NoopAnimationsModule
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(DropdownComponent);
    component = fixture.componentInstance;
    httpMock = TestBed.inject(HttpTestingController);

    // Set required inputs
    component.fieldName = 'testField';
    component.formControl = new FormControl('');
    component.config = { ...defaultConfig };
  });

  afterEach(() => {
    httpMock.verify();
  });

  describe('Component Initialization', () => {
    it('should create', () => {
      expect(component).toBeTruthy();
    });

    it('should initialize with default values', () => {
      expect(component.showDropdown).toBeFalse();
      expect(component.filteredOptions).toEqual([]);
      expect(component.isLoading).toBeFalse();
      expect(component.isDisabled).toBeFalse();
      expect(component.isReadonly).toBeFalse();
    });

    it('should set initial filtered options when options input is provided', () => {
      component.options = mockOptions;
      component.ngOnInit();
      expect(component.filteredOptions).toEqual(mockOptions);
    });

    it('should initialize with preloaded data', () => {
      const preloadedData = { 'testQuery': mockOptions };
      component.preloadedData = preloadedData;
      component.ngOnInit();
      expect(component['apiCache']).toEqual(preloadedData);
    });
  });

  describe('Form Control Management', () => {
    it('should disable form control when isDisabled is true', () => {
      component.isDisabled = true;
      component.ngOnInit();
      expect(component.formControl.disabled).toBeTrue();
    });

    it('should disable form control when isReadonly is true', () => {
      component.isReadonly = true;
      component.ngOnInit();
      expect(component.formControl.disabled).toBeTrue();
    });

    it('should enable form control when not disabled or readonly', () => {
      component.isDisabled = false;
      component.isReadonly = false;
      component.formControl.disable();
      component.ngOnInit();
      expect(component.formControl.enabled).toBeTrue();
    });

    it('should update form control state on input changes', () => {
      component.ngOnInit();
      expect(component.formControl.enabled).toBeTrue();

      component.isDisabled = true;
      component.ngOnChanges({
        isDisabled: {
          currentValue: true,
          previousValue: false,
          firstChange: false,
          isFirstChange: () => false
        }
      });
      expect(component.formControl.disabled).toBeTrue();
    });
  });

  describe('Dropdown Toggle Functionality', () => {
    beforeEach(() => {
      component.ngOnInit();
      fixture.detectChanges();
    });

    it('should not toggle dropdown when disabled', () => {
      component.isDisabled = true;
      component.toggleDropdown();
      expect(component.showDropdown).toBeFalse();
    });

    it('should not toggle dropdown when readonly', () => {
      component.isReadonly = true;
      component.toggleDropdown();
      expect(component.showDropdown).toBeFalse();
    });

    it('should load all options when toggling with empty value', () => {
      spyOn(component, 'loadAllOptions');
      component.formControl.setValue('');
      component.toggleDropdown();
      expect(component.loadAllOptions).toHaveBeenCalled();
    });

    it('should search options when toggling with existing value', () => {
      spyOn(component, 'searchOptions');
      component.formControl.setValue('test');
      component.toggleDropdown();
      expect(component.searchOptions).toHaveBeenCalledWith('test');
    });

    it('should close dropdown when already open', () => {
      component.showDropdown = true;
      component.toggleDropdown();
      expect(component.showDropdown).toBeFalse();
    });

    it('should emit dropdownToggle event', () => {
      spyOn(component.dropdownToggle, 'emit');
      component.toggleDropdown();
      expect(component.dropdownToggle.emit).toHaveBeenCalled();
    });
  });

  describe('Search Functionality', () => {
    beforeEach(() => {
      component.ngOnInit();
      fixture.detectChanges();
    });

    it('should load all options when search term is empty', () => {
      spyOn(component, 'loadAllOptions');
      component.searchOptions('');
      expect(component.loadAllOptions).toHaveBeenCalled();
    });

    it('should filter options with search term', () => {
      spyOn(component, 'loadAllAndFilter');
      component.searchOptions('test');
      expect(component.loadAllAndFilter).toHaveBeenCalledWith('test');
    });

    it('should debounce input changes', fakeAsync(() => {
      spyOn(component, 'searchOptions');
      const inputElement = fixture.debugElement.query(By.css('input'));
      
      inputElement.nativeElement.value = 'test';
      inputElement.nativeElement.dispatchEvent(new Event('input'));
      
      expect(component.searchOptions).not.toHaveBeenCalled();
      
      tick(300);
      expect(component.searchOptions).toHaveBeenCalledWith('test');
    }));

    it('should emit searchChange event on input', fakeAsync(() => {
      spyOn(component.searchChange, 'emit');
      const inputElement = fixture.debugElement.query(By.css('input'));
      
      inputElement.nativeElement.value = 'test';
      inputElement.nativeElement.dispatchEvent(new Event('input'));
      
      tick(300);
      expect(component.searchChange.emit).toHaveBeenCalledWith('test');
    }));
  });

  describe('Option Selection', () => {
    beforeEach(() => {
      component.ngOnInit();
      fixture.detectChanges();
    });

    it('should select option and update form control', () => {
      const option = mockOptions[0];
      component.selectOption(option);
      
      expect(component.formControl.value).toBe(option.ROW_ID);
      expect(component.showDropdown).toBeFalse();
    });

    it('should emit valueChange event on option selection', () => {
      spyOn(component.valueChange, 'emit');
      const option = mockOptions[0];
      component.selectOption(option);
      
      expect(component.valueChange.emit).toHaveBeenCalledWith({
        fieldName: component.fieldName,
        value: option.ROW_ID,
        option: option,
        displayText: option.ROW_ID
      });
    });

    it('should emit optionSelect event', () => {
      spyOn(component.optionSelect, 'emit');
      const option = mockOptions[0];
      component.selectOption(option);
      
      expect(component.optionSelect.emit).toHaveBeenCalledWith(option);
    });

    it('should mark form control as dirty and touched', () => {
      const option = mockOptions[0];
      component.selectOption(option);
      
      expect(component.formControl.dirty).toBeTrue();
      expect(component.formControl.touched).toBeTrue();
    });
  });

  describe('API Integration', () => {
    beforeEach(() => {
      component.ngOnInit();
      fixture.detectChanges();
    });

    it('should load options from API', () => {
      component.loadAllOptions();
      
      const req = httpMock.expectOne(`${environment.baseURL}/api/query-builder/search?queryBuilderId=testQuery`);
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual({ _select: ['ROW_ID'] });
      
      req.flush(mockOptions);
      
      expect(component.filteredOptions).toEqual(mockOptions);
      expect(component.showDropdown).toBeTrue();
    });

    it('should handle API errors gracefully', () => {
      component.loadAllOptions();
      
      const req = httpMock.expectOne(`${environment.baseURL}/api/query-builder/search?queryBuilderId=testQuery`);
      req.error(new ErrorEvent('Network error'));
      
      expect(component.filteredOptions).toEqual([]);
      expect(component.showDropdown).toBeTrue();
      expect(component.isLoading).toBeFalse();
    });

    it('should use cached data when available', () => {
      component['apiCache']['testQuery'] = mockOptions;
      component.loadAllOptions();
      
      httpMock.expectNone(`${environment.baseURL}/api/query-builder/search?queryBuilderId=testQuery`);
      expect(component.filteredOptions).toEqual(mockOptions);
    });

    it('should filter cached data for search', () => {
      component['apiCache']['testQuery'] = mockOptions;
      component.searchOptions('option1');
      
      expect(component.filteredOptions).toEqual([mockOptions[0]]);
      expect(component.showDropdown).toBeTrue();
    });
  });

  describe('Dropdown Types', () => {
    it('should handle type dropdown configuration', () => {
      component.config = { type: 'type', queryBuilderId: 'fieldType' };
      expect(component['getCacheKey']()).toBe('fieldType');
    });

    it('should handle foreignKey dropdown configuration', () => {
      component.config = { type: 'foreignKey', queryBuilderId: 'formDefinition' };
      expect(component['getCacheKey']()).toBe('formDefinition');
    });

    it('should handle id dropdown configuration', () => {
      component.config = { type: 'id', queryBuilderId: 'customId' };
      expect(component['getCacheKey']()).toBe('customId');
    });

    it('should extract foreign key for regular dropdown', () => {
      component.config = { type: 'regular' };
      component.fieldName = 'testField';
      component.fields = [{ fieldName: 'testField', foreginKey: 'customForeignKey' }];
      
      expect(component['getCacheKey']()).toBe('customForeignKey');
    });
  });

  describe('Utility Methods', () => {
    it('should extract original field name correctly', () => {
      expect(component['extractOriginalFieldName']('field_nested_1_2')).toBe('field');
      expect(component['extractOriginalFieldName']('field_group_1')).toBe('field');
      expect(component['extractOriginalFieldName']('field_1')).toBe('field');
      expect(component['extractOriginalFieldName']('simpleField')).toBe('simpleField');
    });

    it('should get display text for different option types', () => {
      const typeOption = { ROW_ID: 'string' };
      const regularOption = { ROW_ID: 'id1', name: 'Test Name' };
      
      component.config = { type: 'type' };
      expect(component['getOptionDisplayText'](typeOption)).toBe('string');
      
      component.config = { type: 'regular' };
      expect(component['getOptionDisplayText'](regularOption)).toBe('Test Name');
    });

    it('should return correct trackBy values', () => {
      const option = mockOptions[0];
      expect(component.trackByOptionId(0, option)).toBe(option.ROW_ID);
      expect(component.trackByKey(0, 'testKey')).toBe('testKey');
    });
  });

  describe('Template Integration', () => {
    beforeEach(() => {
      component.ngOnInit();
      fixture.detectChanges();
    });

    it('should render input element with correct attributes', () => {
      const inputElement = fixture.debugElement.query(By.css('input'));
      expect(inputElement).toBeTruthy();
      expect(inputElement.nativeElement.id).toBe(component.fieldName);
      expect(inputElement.nativeElement.placeholder).toBe(component.config.placeholder);
    });

    it('should render arrow button when showArrowButton is true', () => {
      component.showArrowButton = true;
      fixture.detectChanges();
      
      const buttonElement = fixture.debugElement.query(By.css('.dropdown-arrow-btn'));
      expect(buttonElement).toBeTruthy();
    });

    it('should not render arrow button when showArrowButton is false', () => {
      component.showArrowButton = false;
      fixture.detectChanges();
      
      const buttonElement = fixture.debugElement.query(By.css('.dropdown-arrow-btn'));
      expect(buttonElement).toBeFalsy();
    });

    it('should show dropdown list when showDropdown is true', () => {
      component.showDropdown = true;
      component.filteredOptions = mockOptions;
      fixture.detectChanges();
      
      const dropdownElement = fixture.debugElement.query(By.css('.dropdown-list'));
      expect(dropdownElement).toBeTruthy();
    });

    it('should render options in dropdown list', () => {
      component.showDropdown = true;
      component.filteredOptions = mockOptions;
      fixture.detectChanges();
      
      const optionElements = fixture.debugElement.queryAll(By.css('.dropdown-item'));
      expect(optionElements.length).toBe(mockOptions.length);
    });

    it('should show empty message when no options available', () => {
      component.showDropdown = true;
      component.filteredOptions = [];
      fixture.detectChanges();
      
      const emptyElement = fixture.debugElement.query(By.css('.dropdown-empty'));
      expect(emptyElement).toBeTruthy();
      expect(emptyElement.nativeElement.textContent.trim()).toBe(component.emptyMessage);
    });

    it('should show loading state', () => {
      component.showDropdown = true;
      component.isLoading = true;
      fixture.detectChanges();
      
      const loadingElement = fixture.debugElement.query(By.css('.dropdown-loading'));
      expect(loadingElement).toBeTruthy();
    });
  });

  describe('Cleanup', () => {
    it('should clear timeout on destroy', () => {
      component.searchTimeout = setTimeout(() => {}, 1000);
      spyOn(window, 'clearTimeout');
      
      component.ngOnDestroy();
      expect(clearTimeout).toHaveBeenCalledWith(component.searchTimeout);
    });
  });
});
