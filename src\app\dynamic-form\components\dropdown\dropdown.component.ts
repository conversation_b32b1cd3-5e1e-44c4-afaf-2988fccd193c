import { Component, Input, Output, EventEmitter, OnDestroy, OnInit, OnChanges, SimpleChanges, inject, ChangeDetectorRef } from '@angular/core';
import { FormGroup, FormControl } from '@angular/forms';
import { HttpClient } from '@angular/common/http';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { environment } from '../../../../environments/environment';

export interface DropdownOption {
  ROW_ID: string;
  [key: string]: any;
}

export interface DropdownConfig {
  type: 'type' | 'foreignKey' | 'regular' | 'id';
  apiEndpoint?: string;
  queryBuilderId?: string;
  searchEnabled?: boolean;
  placeholder?: string;
  emptyMessage?: string;
  tooltip?: string;
  maxHeight?: string;
  limit?: number;
}

export interface DropdownValueChangeEvent {
  fieldName: string;
  value: any;
  option: DropdownOption;
  displayText: string;
}

@Component({
  selector: 'app-dropdown',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatIconModule,
    MatTooltipModule
  ],
  templateUrl: './dropdown.component.html',
  styleUrl: './dropdown.component.scss'
})
export class DropdownComponent implements OnInit, OnDestroy, OnChanges {
  // Core inputs
  @Input() fieldName!: string;
  @Input() formControl!: FormControl;
  @Input() config!: DropdownConfig;
  @Input() isDisabled: boolean = false;
  @Input() isReadonly: boolean = false;
  @Input() options: DropdownOption[] = [];
  @Input() selectedValue: any = '';
  @Input() cssClass: string = '';
  @Input() inputId?: string;

  // Advanced configuration
  @Input() preloadedData: { [key: string]: DropdownOption[] } = {};
  @Input() fields: any[] = []; // For extracting original field names
  @Input() searchDebounceTime: number = 300;
  @Input() showArrowButton: boolean = true;
  @Input() autoClose: boolean = true;

  // Outputs
  @Output() valueChange = new EventEmitter<DropdownValueChangeEvent>();
  @Output() searchChange = new EventEmitter<string>();
  @Output() dropdownToggle = new EventEmitter<boolean>();
  @Output() optionSelect = new EventEmitter<DropdownOption>();

  // Internal state
  showDropdown: boolean = false;
  filteredOptions: DropdownOption[] = [];
  isLoading: boolean = false;
  searchTimeout: any;
  
  // Performance optimization: API response cache
  private apiCache: { [key: string]: DropdownOption[] } = {};
  
  // Track when we're setting dropdown values to prevent input conflicts
  private settingDropdownValue: boolean = false;

  private http = inject(HttpClient);
  private cdr = inject(ChangeDetectorRef);

  ngOnInit() {
    // Initialize with preloaded data if available
    if (this.preloadedData && Object.keys(this.preloadedData).length > 0) {
      this.apiCache = { ...this.preloadedData };
    }

    // Preload dropdown data for performance
    this.preloadDropdownData();

    // Set initial filtered options if options are provided
    if (this.options && this.options.length > 0) {
      this.filteredOptions = [...this.options];
    }

    // Update disabled state
    this.updateFormControlDisabledState();
  }

  ngOnChanges(changes: SimpleChanges): void {
    // Update form control disabled state when inputs change
    if (changes['isDisabled'] || changes['isReadonly']) {
      this.updateFormControlDisabledState();
    }

    // Update filtered options when options input changes
    if (changes['options'] && this.options) {
      this.filteredOptions = [...this.options];
    }

    // Update cache when preloaded data changes
    if (changes['preloadedData'] && this.preloadedData) {
      this.apiCache = { ...this.preloadedData };
    }
  }

  ngOnDestroy() {
    // Clear search timeout
    if (this.searchTimeout) {
      clearTimeout(this.searchTimeout);
    }
  }

  private updateFormControlDisabledState(): void {
    if (this.formControl) {
      if (this.isDisabled || this.isReadonly) {
        if (this.formControl.enabled) {
          this.formControl.disable();
        }
      } else {
        if (this.formControl.disabled) {
          this.formControl.enable();
        }
      }
    }
  }

  toggleDropdown(): void {
    // Prevent interaction when disabled/readonly
    if (this.isDisabled || this.isReadonly) {
      return;
    }

    if (!this.showDropdown) {
      const currentValue = this.formControl?.value || '';
      if (currentValue.trim() === '') {
        this.loadAllOptions();
      } else {
        this.searchOptions(currentValue);
      }
    } else {
      this.showDropdown = false;
    }

    this.dropdownToggle.emit(this.showDropdown);
  }

  onInputChange(event: Event): void {
    if (this.settingDropdownValue) {
      return; // Prevent conflicts when setting dropdown values
    }

    const target = event.target as HTMLInputElement;
    const searchTerm = target.value;

    // Clear existing timeout
    if (this.searchTimeout) {
      clearTimeout(this.searchTimeout);
    }

    // Debounce search
    this.searchTimeout = setTimeout(() => {
      this.searchOptions(searchTerm);
      this.searchChange.emit(searchTerm);
    }, this.searchDebounceTime);
  }

  onInputFocus(): void {
    // Auto-open dropdown on focus if not disabled
    if (!this.isDisabled && !this.isReadonly && !this.showDropdown) {
      this.toggleDropdown();
    }
  }

  onInputBlur(): void {
    // Delay hiding dropdown to allow click on dropdown items
    if (this.autoClose) {
      setTimeout(() => {
        this.showDropdown = false;
        this.dropdownToggle.emit(false);
      }, 200);
    }
  }

  selectOption(option: DropdownOption): void {
    this.setDropdownValue(option);
    this.optionSelect.emit(option);
  }

  searchOptions(searchTerm: string): void {
    if (searchTerm.trim() === '') {
      this.loadAllOptions();
      return;
    }

    // For ID dropdowns, use server-side filtering for better performance
    if (this.config.type === 'id') {
      this.loadFromApi(searchTerm);
      return;
    }

    // For other dropdown types, use client-side filtering if data is cached
    this.loadAllAndFilter(searchTerm);
  }

  loadAllOptions(): void {
    const cacheKey = this.getCacheKey();

    // Use preloaded data for superior performance
    if (this.apiCache[cacheKey]) {
      this.filteredOptions = this.apiCache[cacheKey];
      this.showDropdown = true;
      return;
    }

    // Fallback: Load if not preloaded
    this.loadFromApi();
  }

  private getCacheKey(): string {
    if (this.config.queryBuilderId) {
      return this.config.queryBuilderId;
    }
    
    switch (this.config.type) {
      case 'type':
        return 'fieldType';
      case 'foreignKey':
        return 'formDefinition';
      case 'regular':
        // Extract foreign key from field configuration
        const originalFieldName = this.extractOriginalFieldName(this.fieldName);
        const field = this.fields.find(f => f.fieldName === originalFieldName);
        return field?.foreginKey || 'unknown';
      case 'id':
        return this.config.queryBuilderId || 'id';
      default:
        return 'default';
    }
  }

  private loadFromApi(searchTerm?: string): void {
    const apiUrl = this.getApiUrl();
    const payload = this.getApiPayload(searchTerm);

    if (!apiUrl) {
      this.setEmptyDropdownState();
      return;
    }

    this.isLoading = true;
    this.http.post<any[]>(apiUrl, payload, { withCredentials: true }).subscribe({
      next: (response: any) => {
        this.isLoading = false;
        if (Array.isArray(response)) {
          // Only cache if no search term (full data)
          if (!searchTerm || searchTerm.trim() === '') {
            const cacheKey = this.getCacheKey();
            this.apiCache[cacheKey] = response;
          }
          this.filteredOptions = response;
          this.showDropdown = true;
        } else {
          this.setEmptyDropdownState();
        }
      },
      error: () => {
        this.isLoading = false;
        this.setEmptyDropdownState();
      }
    });
  }

  private getApiUrl(): string {
    if (this.config.apiEndpoint) {
      return this.config.apiEndpoint;
    }

    const queryBuilderId = this.getCacheKey();
    return `${environment.baseURL}/api/query-builder/search?queryBuilderId=${queryBuilderId}`;
  }

  private getApiPayload(searchTerm?: string): any {
    const basePayload: any = {
      _select: ["ROW_ID"]
    };

    // Add search filtering if search term is provided
    if (searchTerm && searchTerm.trim() !== '') {
      // For ID dropdowns, use ID field for filtering
      if (this.config.type === 'id') {
        basePayload.ID = {
          CT: searchTerm // CT = Contains operator
        };
      } else {
        // For other dropdown types, use ROW_ID field for filtering
        basePayload.ROW_ID = {
          CT: searchTerm
        };
      }
    }

    if (this.config.limit) {
      basePayload._limit = this.config.limit;
    } else {
      // Default limit for better performance
      basePayload._limit = this.config.type === 'id' ? 20 : 100;
    }

    return basePayload;
  }

  private loadAllAndFilter(searchTerm: string): void {
    const cacheKey = this.getCacheKey();

    // Use preloaded data for superior performance
    if (this.apiCache[cacheKey]) {
      const filtered = this.apiCache[cacheKey].filter(option =>
        option.ROW_ID.toLowerCase().includes(searchTerm.toLowerCase())
      );
      this.filteredOptions = filtered;
      this.showDropdown = true;
      return;
    }

    // Fallback: Load and filter
    this.loadFromApiAndFilter(searchTerm);
  }

  private loadFromApiAndFilter(searchTerm: string): void {
    const cacheKey = this.getCacheKey();

    // Use cached data if available for client-side filtering
    if (this.apiCache[cacheKey]) {
      const filtered = this.apiCache[cacheKey].filter(option => {
        // Filter by ROW_ID for most dropdown types
        if (this.config.type === 'id') {
          return option['ID'] && option['ID'].toLowerCase().includes(searchTerm.toLowerCase());
        } else {
          return option.ROW_ID && option.ROW_ID.toLowerCase().includes(searchTerm.toLowerCase());
        }
      });
      this.filteredOptions = filtered;
      this.showDropdown = true;
      return;
    }

    // Fallback: Load from API and then filter
    const apiUrl = this.getApiUrl();
    const payload = this.getApiPayload();

    if (!apiUrl) {
      this.setEmptyDropdownState();
      return;
    }

    this.isLoading = true;
    this.http.post<any[]>(apiUrl, payload, { withCredentials: true }).subscribe({
      next: (response: any) => {
        this.isLoading = false;
        if (Array.isArray(response)) {
          this.apiCache[cacheKey] = response;
          const filtered = response.filter(option => {
            if (this.config.type === 'id') {
              return option['ID'] && option['ID'].toLowerCase().includes(searchTerm.toLowerCase());
            } else {
              return option.ROW_ID && option.ROW_ID.toLowerCase().includes(searchTerm.toLowerCase());
            }
          });
          this.filteredOptions = filtered;
          this.showDropdown = true;
        } else {
          this.setEmptyDropdownState();
        }
      },
      error: () => {
        this.isLoading = false;
        this.setEmptyDropdownState();
      }
    });
  }

  private setEmptyDropdownState(): void {
    this.filteredOptions = [];
    this.showDropdown = true;
  }

  private setDropdownValue(option: DropdownOption): void {
    // Mark that we're setting a dropdown value to prevent input conflicts
    this.settingDropdownValue = true;

    if (this.formControl) {
      // Get the display text for the input field
      const displayText = this.getOptionDisplayText(option);

      // Set the form control value to ROW_ID (for storage)
      this.formControl.setValue(option.ROW_ID);

      // Set the input element's display value to the human-readable text
      setTimeout(() => {
        const inputElement = document.getElementById(this.inputId || this.fieldName) as HTMLInputElement;
        if (inputElement) {
          inputElement.value = displayText;
        }
      }, 0);

      // Force change detection and validation
      this.formControl.markAsDirty();
      this.formControl.markAsTouched();
      this.formControl.updateValueAndValidity();

      // Force Angular change detection
      this.cdr.detectChanges();
    }

    // Close dropdown
    this.showDropdown = false;

    // Emit value change event
    const displayText = this.getOptionDisplayText(option);
    this.valueChange.emit({
      fieldName: this.fieldName,
      value: option.ROW_ID,
      option: option,
      displayText: displayText
    });

    // Clear the dropdown value setting flag after a short delay
    setTimeout(() => {
      this.settingDropdownValue = false;
    }, 100);
  }

  /**
   * Get the display text for an option (human-readable text to show in input)
   */
  private getOptionDisplayText(option: DropdownOption): string {
    if (!option) return '';

    // For ID fields, return the ID property
    if (this.config.type === 'id') {
      return option['ID'] || option.ROW_ID || '';
    }

    // For type fields, return the ROW_ID
    if (this.config.type === 'type') {
      return option.ROW_ID || '';
    }

    // For other foreign key fields, get the first non-ROW_ID property
    const keys = Object.keys(option).filter(key => key !== 'ROW_ID');
    if (keys.length > 0) {
      return option[keys[0]] || option.ROW_ID || '';
    }

    return option.ROW_ID || '';
  }

  /**
   * Extract the original field name from complex field names like:
   * - fieldName_group_k
   * - fieldName_nested_k_n
   * - fieldName_group_k_multi_l
   * - fieldName_j (for multi-fields)
   */
  private extractOriginalFieldName(fieldName: string): string {
    if (fieldName.includes('_nested_')) {
      return fieldName.split('_nested_')[0];
    } else if (fieldName.includes('_group_')) {
      return fieldName.split('_group_')[0];
    } else if (fieldName.includes('_')) {
      // Handle simple multi-field pattern like fieldName_j
      const parts = fieldName.split('_');
      if (parts.length === 2 && !isNaN(parseInt(parts[1]))) {
        return parts[0];
      }
      return fieldName;
    }
    return fieldName;
  }

  private preloadDropdownData(): void {
    const cacheKey = this.getCacheKey();

    // Skip if already cached
    if (this.apiCache[cacheKey]) {
      return;
    }

    const apiUrl = this.getApiUrl();
    const payload = this.getApiPayload();

    if (!apiUrl) {
      return;
    }

    this.http.post<any[]>(apiUrl, payload, { withCredentials: true }).subscribe({
      next: (response: any) => {
        if (Array.isArray(response)) {
          this.apiCache[cacheKey] = response;
        }
      },
      error: () => {
        // Handle preload error silently
      }
    });
  }

  // Utility methods for template
  getKeys(option: DropdownOption): string[] {
    return Object.keys(option);
  }

  // Performance optimization: trackBy functions
  trackByOptionId(_index: number, option: DropdownOption): string {
    return option.ROW_ID;
  }

  trackByKey(_index: number, key: string): string {
    return key;
  }

  // Computed properties for template
  get inputClass(): string {
    // Start with base form-input class
    let classes = 'form-input';

    // Add any additional CSS classes passed in
    if (this.cssClass) {
      // If cssClass already contains 'form-input', don't duplicate it
      if (this.cssClass.includes('form-input')) {
        classes = this.cssClass;
      } else {
        classes += ` ${this.cssClass}`;
      }
    }

    // Add disabled state if needed
    if (this.isDisabled || this.isReadonly) {
      classes += ' disabled';
    }

    return classes;
  }

  get dropdownArrowIcon(): string {
    return this.showDropdown ? 'keyboard_arrow_up' : 'keyboard_arrow_down';
  }

  get emptyMessage(): string {
    return this.config.emptyMessage || 'No options found';
  }

  get placeholderText(): string {
    return this.config.placeholder || '';
  }

  get tooltipText(): string {
    return this.config.tooltip || 'Show options';
  }

  get dropdownMaxHeight(): string {
    return this.config.maxHeight || '200px';
  }
}
