import { Component, Input, Output, EventEmitter, OnInit, OnD<PERSON>roy, inject } from '@angular/core';
import { FormGroup, ReactiveFormsModule } from '@angular/forms';
import { HttpClient } from '@angular/common/http';
import { CommonModule } from '@angular/common';
import { environment } from '../../../../environments/environment';

// Angular Material imports
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';

// Dropdown component import
import { DropdownComponent, DropdownConfig, DropdownValueChangeEvent } from '../dropdown/dropdown.component';

@Component({
  selector: 'app-initial-input',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatButtonModule,
    MatIconModule,
    MatTooltipModule,
    DropdownComponent
  ],
  templateUrl: './initial-input.component.html',
  styleUrl: './initial-input.component.scss'
})
export class InitialInputComponent implements OnInit, OnDestroy {
  @Input() form!: FormGroup;
  @Input() tableName!: string;
  @Input() screenName!: string;
  @Input() showValidation: boolean = false;

  @Output() loadDataAndBuildForm = new EventEmitter<void>();
  @Output() viewData = new EventEmitter<void>();
  @Output() validationChange = new EventEmitter<boolean>();

  // Note: ID dropdown properties moved to unified DropdownComponent

  private http = inject(HttpClient);

  ngOnInit() {
    // Component initialization
  }

  ngOnDestroy() {
    // Cleanup handled by unified DropdownComponent
  }

  // Note: ID dropdown methods moved to unified DropdownComponent

  getInputClass(): string {
    return this.showValidation ? 'invalid-input' : '';
  }

  // Helper method to extract part before comma for query-builder API calls
  private extractQueryBuilderId(tableName?: string): string {
    const nameToUse = tableName || this.screenName || this.tableName;
    if (nameToUse && nameToUse.includes(',')) {
      return nameToUse.split(',')[0].trim();
    }
    return nameToUse;
  }

  // Note: searchIdOptions, selectIdOption, and loadAllIds methods moved to unified DropdownComponent

  onAddClick(): void {
    this.loadDataAndBuildForm.emit();
  }

  onEditClick(): void {
    this.loadDataAndBuildForm.emit();
  }

  onViewClick(): void {
    this.viewData.emit();
  }

  onMaintenanceClick(): void {
    // Placeholder for maintenance functionality
  }

  // Configuration method for unified dropdown component
  getIdDropdownConfig(): DropdownConfig {
    return {
      type: 'id',
      queryBuilderId: this.extractQueryBuilderId(),
      searchEnabled: true,
      placeholder: 'Enter ID',
      emptyMessage: 'No IDs found',
      tooltip: 'Show ID suggestions',
      limit: 100
    };
  }

  // Event handler for unified dropdown component
  onDropdownValueChange(event: DropdownValueChangeEvent): void {
    // The form control is already updated by the dropdown component
    // Reset validation state when a valid ID is selected
    if (event.value && event.value.trim() !== '') {
      this.showValidation = false;
      this.validationChange.emit(false);
    }
  }

  // Helper method to get FormControl with proper typing
  getFormControl(fieldName: string): any {
    return this.form.get(fieldName);
  }
}
